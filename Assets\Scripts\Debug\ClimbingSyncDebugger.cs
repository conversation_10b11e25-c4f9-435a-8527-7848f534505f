using UnityEngine;
using Fusion;

/// <summary>
/// Отладчик синхронизации позиции во время скалолазания
/// </summary>
public class ClimbingSyncDebugger : MonoBeh<PERSON>our {
    [Header("Debug Settings")]
    public bool showDebugInfo = true;
    public bool showGizmos = true;
    public KeyCode toggleKey = KeyCode.F6;
    
    [Header("Sync Monitoring")]
    public float positionTolerance = 0.5f; // Допустимое расхождение позиций
    public bool logDesyncEvents = true;
    
    private PlayerController[] allPlayers;
    private float lastUpdateTime;
    
    private void Start() {
        RefreshPlayersList();
    }
    
    private void Update() {
        if (Input.GetKeyDown(toggleKey)) {
            showDebugInfo = !showDebugInfo;
            Debug.Log($"Climbing Sync Debug: {(showDebugInfo ? "ON" : "OFF")}");
        }
        
        // Обновляем список игроков каждую секунду
        if (Time.time - lastUpdateTime >= 1f) {
            RefreshPlayersList();
            CheckSynchronization();
            lastUpdateTime = Time.time;
        }
    }
    
    private void RefreshPlayersList() {
        allPlayers = FindObjectsOfType<PlayerController>();
    }
    
    /// <summary>
    /// Проверяет синхронизацию позиций игроков во время скалолазания
    /// </summary>
    private void CheckSynchronization() {
        if (!logDesyncEvents || allPlayers == null) return;
        
        foreach (var player in allPlayers) {
            if (player == null || !player.IsClimbing) continue;
            
            // Проверяем расхождение между реальной позицией и сетевой
            Vector3 realPos = player.transform.position;
            Vector3 networkPos = player.ClimbingPosition;
            
            if (networkPos != Vector3.zero) {
                float distance = Vector3.Distance(realPos, networkPos);
                
                if (distance > positionTolerance) {
                    Debug.LogWarning($"CLIMBING DESYNC: Player {player.name} - Real: {realPos}, Network: {networkPos}, Distance: {distance:F2}m");
                }
            }
        }
    }
    
    private void OnGUI() {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(Screen.width - 350, 10, 340, 400));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("=== CLIMBING SYNC DEBUG ===");
        GUILayout.Label($"Press {toggleKey} to toggle");
        
        GUILayout.Space(10);
        
        if (allPlayers != null) {
            GUILayout.Label($"Total Players: {allPlayers.Length}");
            
            int climbingCount = 0;
            int desyncCount = 0;
            
            foreach (var player in allPlayers) {
                if (player == null) continue;
                
                if (player.IsClimbing) {
                    climbingCount++;
                    
                    Vector3 realPos = player.transform.position;
                    Vector3 networkPos = player.ClimbingPosition;
                    
                    GUILayout.Space(5);
                    GUILayout.Label($"=== {player.name} ===");
                    
                    // Определяем роль игрока
                    string role = "Unknown";
                    if (player.Object.HasStateAuthority) {
                        role = "Host/Server";
                        GUI.color = Color.green;
                    } else if (player.Object.HasInputAuthority) {
                        role = "Local Client";
                        GUI.color = Color.yellow;
                    } else {
                        role = "Remote Client";
                        GUI.color = Color.cyan;
                    }
                    
                    GUILayout.Label($"Role: {role}");
                    GUI.color = Color.white;
                    
                    GUILayout.Label($"IsClimbing: {player.IsClimbing}");
                    GUILayout.Label($"Real Pos: {realPos.ToString("F1")}");
                    GUILayout.Label($"Net Pos: {networkPos.ToString("F1")}");
                    
                    if (networkPos != Vector3.zero) {
                        float distance = Vector3.Distance(realPos, networkPos);
                        
                        if (distance > positionTolerance) {
                            GUI.color = Color.red;
                            GUILayout.Label($"⚠️ DESYNC: {distance:F2}m");
                            desyncCount++;
                        } else {
                            GUI.color = Color.green;
                            GUILayout.Label($"✅ SYNCED: {distance:F2}m");
                        }
                        GUI.color = Color.white;
                    }
                    
                    if (player.CurrentHold != null) {
                        GUILayout.Label($"Hold: {player.CurrentHold.name}");
                    }
                }
            }
            
            GUILayout.Space(10);
            GUILayout.Label($"Climbing Players: {climbingCount}");
            
            if (desyncCount > 0) {
                GUI.color = Color.red;
                GUILayout.Label($"⚠️ Desynced: {desyncCount}");
            } else if (climbingCount > 0) {
                GUI.color = Color.green;
                GUILayout.Label("✅ All Synced");
            }
            GUI.color = Color.white;
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Force Sync All")) {
            ForceSyncAll();
        }
        
        if (GUILayout.Button("Refresh Players")) {
            RefreshPlayersList();
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
    
    /// <summary>
    /// Принудительно синхронизирует всех игроков
    /// </summary>
    private void ForceSyncAll() {
        if (allPlayers == null) return;
        
        foreach (var player in allPlayers) {
            if (player == null || !player.IsClimbing) continue;
            
            if (player.ClimbingPosition != Vector3.zero) {
                player.KCC.SetPosition(player.ClimbingPosition);
                Debug.Log($"Force synced player {player.name} to {player.ClimbingPosition}");
            }
        }
    }
    
    private void OnDrawGizmos() {
        if (!showGizmos || allPlayers == null) return;
        
        foreach (var player in allPlayers) {
            if (player == null || !player.IsClimbing) continue;
            
            Vector3 realPos = player.transform.position;
            Vector3 networkPos = player.ClimbingPosition;
            
            if (networkPos == Vector3.zero) continue;
            
            // Показываем реальную позицию
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(realPos, 0.3f);
            
            // Показываем сетевую позицию
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(networkPos, 0.25f);
            
            // Линия между позициями
            float distance = Vector3.Distance(realPos, networkPos);
            if (distance > positionTolerance) {
                Gizmos.color = Color.red;
            } else {
                Gizmos.color = Color.yellow;
            }
            Gizmos.DrawLine(realPos, networkPos);
            
            // Показываем зацепку
            if (player.CurrentHold != null) {
                Gizmos.color = Color.cyan;
                Gizmos.DrawLine(realPos, player.CurrentHold.transform.position);
            }
        }
    }
}

using Fusion;
using UnityEngine;
using SimpleFPS;
using System.Collections.Generic;

public sealed class KlassikiGameMode : NetworkBehaviour, IGameModeConfig {

    public static KlassikiGameMode Instance { get; private set; }

    [Header("Gameplay Settings")]
    [SerializeField] private float lobbyDuration = 30f;
    [SerializeField] private float matchDuration = 90f;
    [SerializeField] private float respawnTime = 5f;

    [Header("References")]
    [SerializeField] private Transform loserSpawnPoint;
    [SerializeField] private GameManager gameManager;
    [TextArea][SerializeField] private string modeDescription = "Hop‑scotch. Reach finish or fall off.";
    [SerializeField] private BreakablePlatform breakablePlatform1;
    [SerializeField] private BreakablePlatform breakablePlatform2;
    [SerializeField] private BreakablePlatform breakablePlatform3;
    [SerializeField] private BreakablePlatform breakablePlatform4;

    [Header("Runtime")]
    private readonly HashSet<PlayerRef> finishedPlayers = new HashSet<PlayerRef>();   // players who reached finish trigger
    private readonly HashSet<PlayerRef> failedPlayers = new HashSet<PlayerRef>();     // players who fell into loser trigger
    private bool matchAlreadyEnded = false;

    #region IGameModeConfig
    public float LobbyDuration => lobbyDuration;
    public float MatchDuration => matchDuration;
    public float PlayerRespawnTime => respawnTime;
    public Transform LoserSpawnPoint => loserSpawnPoint;
    public string ModeDescription => modeDescription;
    #endregion

    private void Awake() {
        Instance = this;
        gameManager = GameManager.Instance;
    }

    public override void Spawned() {
        if (!Object.HasStateAuthority) {
            enabled = false;
        }

        SetupRandomBreakable(breakablePlatform1, breakablePlatform2);

        SetupRandomBreakable(breakablePlatform3, breakablePlatform4);
    }


    public void RegisterFinishedPlayer(PlayerRef pr) {
        finishedPlayers.Add(pr);
        TryEndMatch();
    }

    public void RegisterFailedPlayer(PlayerRef pr) {
        failedPlayers.Add(pr);
        TryEndMatch();
    }

    public void HandlePlayerDisconnected(PlayerRef pr) {
        // Отключившиеся игроки считаются проигравшими
        failedPlayers.Add(pr);
        TryEndMatch();
    }


    private void Update() {
        if (!Object.HasStateAuthority) {
            return;
        }

        TryEndMatch();
    }

    private void TryEndMatch() {
        if (matchAlreadyEnded) {
            return;
        }

        // Проверяем условия досрочного завершения
        if (ShouldEndMatch()) {
            matchAlreadyEnded = true;

            // Все игроки, которые не дошли до финиша, становятся проигравшими
            MarkRemainingPlayersAsFailed();

            gameManager.EndMatch();
        }
    }

    private bool ShouldEndMatch() {
        int connectedPlayers = 0;
        int allFinishedOrFailed = 0;

        foreach (var kv in gameManager.PlayerData) {
            if (!kv.Value.IsConnected) continue;

            connectedPlayers++;

            if (finishedPlayers.Contains(kv.Key) || failedPlayers.Contains(kv.Key)) {
                allFinishedOrFailed++;
            }
        }

        // Игра заканчивается если:
        // 1. Все игроки либо дошли до финиша, либо проиграли
        // 2. Все живые игроки дошли до финиша
        // 3. Все игроки мертвы
        return allFinishedOrFailed == connectedPlayers ||
               AllConnectedPlayersAreDead() ||
               AllConnectedPlayersFinished();
    }

    private bool AllConnectedPlayersAreDead() {
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Value.IsAlive) {
                return false;
            }
        }
        return true;
    }

    private bool AllConnectedPlayersFinished() {
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Value.IsAlive && !finishedPlayers.Contains(kv.Key)) {
                return false;
            }
        }
        return true;
    }

    private void MarkRemainingPlayersAsFailed() {
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && !finishedPlayers.Contains(kv.Key) && !failedPlayers.Contains(kv.Key)) {
                var pd = kv.Value;
                // Only set IsAlive = false if squid game mode is enabled
                if (gameManager.IsSquidGameMode) {
                    pd.IsAlive = false;
                }
                gameManager.PlayerData.Set(kv.Key, pd);
                failedPlayers.Add(kv.Key);
            }
        }
    }

    public void SetupRandomBreakable(BreakablePlatform a, BreakablePlatform b) {
        if (!Object.HasStateAuthority) { return; }

        a.CanBreak = false;
        b.CanBreak = false;
        (Random.Range(0, 2) == 0 ? a : b).CanBreak = true;
    }


    public void OnRoundStarted() { }

    public void OnRoundEnded() {
        HandleTimeExpired();

        // Award points to players who finished and are still alive
        foreach (var playerRef in finishedPlayers) {
            if (gameManager.PlayerData.TryGet(playerRef, out var pd) && pd.IsConnected && pd.IsAlive) {
                pd.Money += 100;
                gameManager.PlayerData.Set(playerRef, pd);
            }
        }
    }

    public void DetermineWinnersAndLosers() { }

    public void ResetModeState() {
        finishedPlayers.Clear();
        failedPlayers.Clear();
        matchAlreadyEnded = false;
    }

    private void HandleTimeExpired() {
        // При истечении времени все игроки, которые не дошли до финиша, становятся проигравшими
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && !finishedPlayers.Contains(kv.Key) && !failedPlayers.Contains(kv.Key)) {
                var pd = kv.Value;
                // Only set IsAlive = false if squid game mode is enabled
                if (gameManager.IsSquidGameMode) {
                    pd.IsAlive = false;
                }
                gameManager.PlayerData.Set(kv.Key, pd);
                failedPlayers.Add(kv.Key);
            }
        }
    }

}

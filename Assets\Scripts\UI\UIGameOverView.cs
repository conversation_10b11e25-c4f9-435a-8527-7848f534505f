﻿using Fusion;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace SimpleFPS {
    public class UIGameOverView : MonoBehaviour {
        public TextMeshProUGUI FinalText;
        public TextMeshProUGUI VictoryText;
        public TextMeshProUGUI DefeatText;
        public TextMeshProUGUI WinnerNameText;
        public AudioSource GameOverMusic;
        public AudioClip VictoryClip;
        public AudioClip DefeatClip;

        private UIGame gameUI;

        public void GoToMenu() {
            gameUI.GoToMenu();
        }

        private void Awake() {
            gameUI = GetComponentInParent<UIGame>();

            // Слушаем загрузку любой сцены, чтобы обновиться заново.
            SceneManager.sceneLoaded += OnSceneLoaded;
        }
        private void OnDestroy() {
            SceneManager.sceneLoaded -= OnSceneLoaded;
        }
        private bool IsGameManagerReady() {
            return gameUI != null &&
                   gameUI.GameManager != null &&
                   gameUI.GameManager.Object != null &&
                   gameUI.GameManager.Object.IsValid;
        }

        private bool HasWinner() {
            // Проверяем, находимся ли мы на сцене победы
            bool winnerScene = SceneManager.GetActiveScene().name == gameUI.GameManager.VictorySceneName;

            if (winnerScene) {
                // На сцене победы есть победитель, если lastWinner не пустой
                string netWinnerName = gameUI.GameManager.lastWinner.ToString();
                return !string.IsNullOrEmpty(netWinnerName);
            }
            else {
                // На других сценах проверяем, есть ли живые игроки
                if (gameUI.GameManager.PlayerData.TryGet(gameUI.Runner.LocalPlayer, out var localPlayerData)) {
                    return localPlayerData.IsAlive;
                }
                return false;
            }
        }

        private void OnEnable() {
            if (!IsGameManagerReady()) {
                return;
            }

            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;

            // Проверяем, есть ли победитель
            /*bool hasWinner = HasWinner();
            if (hasWinner) {
                GameOverMusic.clip = VictoryClip;
            }
            else {
                GameOverMusic.clip = DefeatClip;
            }
            GameOverMusic.PlayDelayed(5f);*/

            RefreshText();
        }

        private void OnSceneLoaded(Scene scene, LoadSceneMode mode) {
            if (isActiveAndEnabled) {
                RefreshText();
            }
        }
        private void RefreshText() {
            bool winnerScene =
                SceneManager.GetActiveScene().name == gameUI.GameManager.VictorySceneName;

            if (winnerScene) {
                ShowFinalTitle();
            }
            else {
                ShowDifferentTitle();
            }
        }

        private void ShowFinalTitle() {
            string netWinnerName = gameUI.GameManager.lastWinner.ToString();
            if (!string.IsNullOrEmpty(netWinnerName)) {
                WinnerNameText.text = $"{netWinnerName}, is the Winner!";
            }
            FinalText.gameObject.SetActive(true);
            VictoryText.gameObject.SetActive(false);
            DefeatText.gameObject.SetActive(false);
        }

        private void ShowDifferentTitle() {
            bool localPlayerIsWinner = false;
            if (gameUI.GameManager.PlayerData.TryGet(gameUI.Runner.LocalPlayer, out var localPlayerData)) {
                localPlayerIsWinner = localPlayerData.IsAlive;
            }
            if (localPlayerIsWinner) {
                ShowVictoryTitle();
            }
            else {
                ShowDefeatTitle();
            }
        }

        public void ShowVictoryTitle() {
            VictoryText.gameObject.SetActive(true);
            DefeatText.gameObject.SetActive(false);
            FinalText.gameObject.SetActive(false);
        }

        public void ShowDefeatTitle() {
            DefeatText.gameObject.SetActive(true);
            VictoryText.gameObject.SetActive(false);
            FinalText.gameObject.SetActive(false);
        }

        public void UpdateWinnerName() {
            string netWinnerName = gameUI.GameManager.lastWinner.ToString();
            if (!string.IsNullOrEmpty(netWinnerName)) {
                WinnerNameText.text = $"{netWinnerName}, is the Winner!";
            }
        }
    }
}

using UnityEngine;
using Fusion;
using Fusion.Addons.SimpleKCC;
using Cinemachine;
using System.Collections.Generic;
using ExitGames.Client.Photon.StructWrapping;
using SimpleFPS;
using DG.Tweening;

[DefaultExecutionOrder(-5)]
public class PlayerController : NetworkBehaviour {

    #region Fields
    [Header("Components")]
    public SimpleKCC KCC;
    public PlayerHealth health;

    //public HitboxRoot hitboxRoot;
    public Animator animator;
    public static PlayerController Local;

    [Header("Setup")]
    public float moveSpeed = 6f;
    public float sprintMultiplier = 2f;  // Add sprint multiplier
    public float jumpForce = 10f;
    public float useRange = 4f;
    public AudioSource jumpSound;
    public AudioClip[] jumpClips;
    public Transform cameraHandle;
    public GameObject thirdPersonRoot;
    public Transform firstPersonItemHolder;
    public Transform thirdPersonItemHolder;
    public Transform laserBeamSource;
    public Transform laserBeamFX1;
    public Transform laserBeamFX2;
    public GameObject leaderIndicator;
    public Transform blindfoldAnchor; // Position where blindfold should be placed
    public SkinnedMeshRenderer teamMaterialRenderer; // Renderer for team material changes
    public GameObject shotgunFx;
    public GameObject duckMask;
    public float pickupRadius = 2f; // Configurable pickup radius
    public float pickupRange = 4f; // Maximum distance for pickup detection
    public float maxPickupRange = 8f;


    [Header("Movement")]
    public float upGravity = 15f;
    public float downGravity = 25f;
    public float groundAcceleration = 55f;
    public float groundDeceleration = 25f;
    public float airAcceleration = 25f;
    public float airDeceleration = 1.3f;


    // Networked properties
    [Networked] public NetworkObject CurrentItem { get; set; }
    [Networked] private NetworkButtons PreviousButtons { get; set; }
    [Networked] private int JumpCount { get; set; }
    [Networked] private Vector3 MoveVelocity { get; set; }
    [Networked] public bool IsLeader { get; set; }
    [Networked] public int TeamId { get; set; } // 0 = no team, 1 = blue, 2 = red
    [Networked] public bool IsFrozen { get; set; }
    [Networked] public bool IsMovementLocked { get; set; }
    [Networked] public bool IsDancing { get; set; } = false;
    [Networked] public TickTimer DanceTimer { get; set; }

    // Climbing system
    [Networked] public bool IsClimbing { get; set; } = false;
    [Networked] public NetworkObject CurrentHold { get; set; } = null;
    [Networked] public Vector3 ClimbingPosition { get; set; } = Vector3.zero; // Синхронизированная позиция во время скалолазания

    // Кэш для оптимизации поиска зацепок
    private static ClimbingHold[] cachedHolds;
    private static float lastHoldsCacheTime;
    private const float HOLDS_CACHE_DURATION = 2f; // Обновляем кэш каждые 2 секунды

    // Local variables
    private NetworkObject prevItem;
    private int visibleJumpCount;
    private SceneObjects sceneObjects;
    private Dictionary<int, GameObject> fppModels; // key = ItemID, value = FPP object
    private GameObject currentFPPModel;             // currently active FPP model
    private string lastSentNickname;

    #endregion

    #region Initialization
    public override void Spawned() {
        SetPlayerNickname();
        if (HasInputAuthority) {
            Local = this;
        }


        string bodyLayerName = HasInputAuthority ? "LocalPlayerBody" : "ClientPlayerBody";
        int bodyLayer = LayerMask.NameToLayer(bodyLayerName);
        int hitboxLayer = LayerMask.NameToLayer("Hitbox");

        // Validate layer indices before using them
        if (bodyLayer == -1) {
            bodyLayer = 0; // Default layer as fallback
        }

        if (hitboxLayer == -1) {
            hitboxLayer = 0; // Default layer as fallback
        }

        SetLayerRecursivelyExceptHitboxes(thirdPersonRoot, bodyLayer, hitboxLayer);


        if (!HasInputAuthority) {
            foreach (var vcam in GetComponentsInChildren<CinemachineVirtualCamera>(true)) {
                vcam.enabled = false;
            }
        }

        sceneObjects = Runner.GetSingleton<SceneObjects>();

        // Duck voice effect should be manually added to NetworkRunner via DuckSoundSetup component

        // Restore team material if in DodgeBall mode
        if (DodgeBallGameMode.Instance != null) {
            DodgeBallGameMode.Instance.RestorePlayerTeamOnSpawn(this);
        }

        // Restore team material if in Knife mode
        if (KnifeGameMode.Instance != null) {
            KnifeGameMode.Instance.RestorePlayerTeamOnSpawn(this);
        }
    }



    /// <summary>Recursively sets layer but keeps Hitbox objects on a dedicated layer.</summary>
    private void SetLayerRecursivelyExceptHitboxes(GameObject obj, int bodyLayer, int hitboxLayer) {
        // Validate layer values are within valid range
        if (bodyLayer < 0 || bodyLayer > 31) {
            return;
        }

        if (hitboxLayer < 0 || hitboxLayer > 31) {
            return;
        }

        if (obj.GetComponent<Hitbox>() != null) {        // keep real hitboxes
            obj.layer = hitboxLayer;
        }
        else {
            obj.layer = bodyLayer;
        }

        foreach (Transform child in obj.transform) {
            SetLayerRecursivelyExceptHitboxes(child.gameObject, bodyLayer, hitboxLayer);
        }
    }

    // Recursively sets the layer for the given GameObject and its children.
    private void SetLayerRecursively(GameObject obj, int layer) {
        obj.layer = layer;
        foreach (Transform child in obj.transform) {
            SetLayerRecursively(child.gameObject, layer);
        }
    }
    #endregion

    #region Climbing System
    /// <summary>
    /// Начать скалолазание на указанной зацепке
    /// </summary>
    public void StartClimbing(ClimbingHold hold) {
        if (hold == null || IsClimbing) return;

        IsClimbing = true;
        CurrentHold = hold.Object;

        // Останавливаем движение
        MoveVelocity = Vector3.zero;

        // Отключаем гравитацию
        KCC.SetGravity(0f);

        // Включаем анимацию висения на всех клиентах
        RPC_SetClimbingAnimation(true);

        // НЕ меняем позицию игрока сразу - он должен висеть там, где схватился
        // Позиционирование будет происходить в FixedUpdate
    }

    /// <summary>
    /// Прекратить скалолазание
    /// </summary>
    public void StopClimbing() {
        if (!IsClimbing) return;

        IsClimbing = false;
        CurrentHold = null;
        ClimbingPosition = Vector3.zero; // Сбрасываем сетевую позицию

        // Восстанавливаем гравитацию
        KCC.SetGravity(-downGravity);

        // Отключаем анимацию висения на всех клиентах
        RPC_SetClimbingAnimation(false);
    }

    /// <summary>
    /// Принудительно отцепиться от текущей зацепки
    /// </summary>
    public void ForceReleaseHold() {
        if (IsClimbing && CurrentHold != null) {
            ClimbingHold hold = CurrentHold.GetComponent<ClimbingHold>();
            if (hold != null) {
                // Прямой вызов RPC через зацепку
                hold.RequestDrop();
            }
            else {
                // Если зацепка не найдена, просто останавливаем скалолазание
                StopClimbing();
            }
        }
    }



    /// <summary>
    /// Обработка ввода для скалолазания
    /// </summary>
    private void HandleClimbingInput(bool isHoldingClimb, bool justPressedClimb, bool justReleasedClimb, NetworkedInput input) {
        if (IsClimbing) {
            // Если игрок уже скалолазит
            if (justReleasedClimb) {
                // Используем принудительное отцепление
                ForceReleaseHold();
            }
            // Прыжок теперь обрабатывается в основной логике движения
        }
        else {
            // Если игрок не скалолазит, но нажал E
            if (justPressedClimb) {
                // Ищем зацепку для захвата
                ClimbingHold nearbyHold = FindClimbingHoldToGrab();
                if (nearbyHold != null) {
                    nearbyHold.RequestToUse(Object.InputAuthority);
                }
            }
        }
    }

    /// <summary>
    /// Получить кэшированный список зацепок (обновляется раз в 2 секунды)
    /// </summary>
    private static ClimbingHold[] GetCachedHolds() {
        float currentTime = Time.time;

        // Обновляем кэш если прошло достаточно времени или кэш пустой
        if (cachedHolds == null || currentTime - lastHoldsCacheTime > HOLDS_CACHE_DURATION) {
            cachedHolds = FindObjectsOfType<ClimbingHold>();
            lastHoldsCacheTime = currentTime;
        }

        return cachedHolds;
    }

    /// <summary>
    /// Найти зацепку для захвата - оптимизированная версия с кэшированием
    /// </summary>
    private ClimbingHold FindClimbingHoldToGrab() {
        // Используем кэшированный список зацепок
        ClimbingHold[] allHolds = GetCachedHolds();

        ClimbingHold bestHold = null;
        float bestScore = float.MaxValue;

        Vector3 playerPos = transform.position;
        Vector3 cameraPos = cameraHandle.position;
        Vector3 lookDirection = cameraHandle.forward;

        // Быстрая предварительная фильтрация по расстоянию
        const float MAX_GRAB_DISTANCE = 8f; // Максимальное расстояние для рассмотрения зацепки

        foreach (ClimbingHold hold in allHolds) {
            if (hold == null || !hold.IsCanBeUse()) continue;

            Vector3 holdPos = hold.transform.position;

            // Быстрая проверка расстояния перед более дорогими вычислениями
            float sqrDistance = (playerPos - holdPos).sqrMagnitude;
            if (sqrDistance > MAX_GRAB_DISTANCE * MAX_GRAB_DISTANCE) continue;

            // Проверяем, что игрок в радиусе захвата ЗАЦЕПКИ
            if (!hold.IsPlayerInRange(this)) continue;

            Vector3 directionToHold = (holdPos - cameraPos).normalized;

            // Проверяем, что зацепка в направлении взгляда
            float dot = Vector3.Dot(lookDirection, directionToHold);
            if (dot < 0.3f) continue; // Угол больше 70 градусов - слишком сбоку

            // Считаем "очки" - комбинация расстояния и угла
            float distance = Mathf.Sqrt(sqrDistance); // Вычисляем реальное расстояние только если нужно
            float score = distance * (2f - dot); // Чем ближе и прямее, тем меньше очков

            if (score < bestScore) {
                bestScore = score;
                bestHold = hold;
            }
        }

        return bestHold;
    }

    /// <summary>
    /// Принудительно обновить кэш зацепок (вызывать при добавлении/удалении зацепок)
    /// </summary>
    public static void RefreshHoldsCache() {
        cachedHolds = null;
        lastHoldsCacheTime = 0f;
    }
    #endregion


    #region Main Update Loop
    public override void FixedUpdateNetwork() {

        // Only block movement for GameOver state
        if (sceneObjects?.Gameplay != null && sceneObjects.Gameplay.State == EGameplayState.GameOver) {
            MovePlayer();
            return;
        }

        if (!health.IsAlive) {
            // Dead players should not move - set velocity to zero
            MovePlayer(Vector3.zero, 0f);
            KCC.SetColliderLayer(LayerMask.NameToLayer("Ignore Raycast"));
            KCC.SetCollisionLayerMask(LayerMask.GetMask("Default"));
            //hitboxRoot.HitboxRootActive = false;
            return;
        }

        if (GetInput(out NetworkedInput input)) {
            ProcessInput(input);
        }
        else {
            MovePlayer();
            RefreshCamera();
        }
    }

    public override void Render() {
        if (sceneObjects.Gameplay.State == EGameplayState.GameOver) {
            return;
        }

        if (CurrentItem != prevItem) {
            OnItemChanged();
            prevItem = CurrentItem;
        }

        // Дополнительная синхронизация позиции для клиентов во время скалолазания
        if (IsClimbing && !Object.HasStateAuthority && ClimbingPosition != Vector3.zero) {
            // Плавно интерполируем к сетевой позиции
            Vector3 currentPos = transform.position;
            Vector3 targetPos = ClimbingPosition;
            float distance = Vector3.Distance(currentPos, targetPos);

            // Если расстояние большое, телепортируем, иначе плавно двигаем
            if (distance > 2f) {
                KCC.SetPosition(targetPos);
            }
            else if (distance > 0.1f) {
                Vector3 lerpedPos = Vector3.Lerp(currentPos, targetPos, Time.deltaTime * 10f);
                KCC.SetPosition(lerpedPos);
            }
        }

        Vector3 localVelocity = transform.InverseTransformDirection(KCC.RealVelocity);
        localVelocity.y = 0f;


        // Handle dancing animation
        if (IsDancing) {
            if (DanceTimer.ExpiredOrNotRunning(Runner)) {
                IsDancing = false;
                if (animator != null) {
                    animator.Play("HockeyPlayer_Idle");
                }
            }
            else {
                if (animator != null && !animator.GetCurrentAnimatorStateInfo(0).IsName("Dancing")) {
                    animator.Play("Dancing");
                }
            }
        }
        else if (IsClimbing) {
            // Climbing animations - останавливаем обычные анимации движения
            animator.SetFloat("moveX", 0f);
            animator.SetFloat("moveZ", 0f);
            animator.SetBool("isGrounded", false); // Игрок висит, не на земле

            // Анимация висения уже установлена в StartClimbing через isClimbing = true
        }
        else {
            // Normal movement animations only when not dancing and not climbing
            animator.SetFloat("moveX", localVelocity.x);
            animator.SetFloat("moveZ", localVelocity.z);

            if (JumpCount > visibleJumpCount) {
                visibleJumpCount = JumpCount;
                animator.SetTrigger("jump");
                if (jumpSound != null && jumpClips != null && jumpClips.Length > 0) {
                    jumpSound.clip = jumpClips[Random.Range(0, jumpClips.Length)];
                    // jumpSound.Play(); // Uncomment this line to play the jump sound
                }
            }
            animator.SetBool("isGrounded", KCC.IsGrounded);
        }
    }

    private void LateUpdate() {
        if (!HasInputAuthority)
            return;
        RefreshCamera();
    }
    #endregion


    #region Nickname
    private void SetPlayerNickname() {
        if (!HasInputAuthority) return;
        string nick = PlayerPrefs.GetString("Photon.Menu.Username", "Player");

        if (lastSentNickname != nick) {
            lastSentNickname = nick;
            RPC_SetPlayerNickname(Object.InputAuthority, nick);
        }
    }
    #endregion


    #region Input Processing
    // Processes player input.
    private void ProcessInput(NetworkedInput input) {
        // Handle look rotation input
        KCC.AddLookRotation(input.LookRotationDelta, -89f, 89f);

        // Handle gravity (не применяем гравитацию во время скалолазания)
        if (!IsClimbing) {
            if (KCC.RealVelocity.y >= 0f) {
                KCC.SetGravity(-upGravity);
            }
            else {
                KCC.SetGravity(-downGravity);
            }
        }

        // If player is frozen, don't process movement input
        if (IsFrozen) {
            MovePlayer(Vector3.zero, 0f);
            RefreshCamera();
            return;
        }

        // Block movement during PreMatch if player is alive
        bool isPreMatch = sceneObjects?.Gameplay != null && sceneObjects.Gameplay.State == EGameplayState.PreMatch;
        bool canMove = !IsMovementLocked && !(isPreMatch && health.IsAlive) && !IsClimbing && !IsDancing;

        Vector3 inputDirection = Vector3.zero;
        float jumpImpulse = 0f;

        if (canMove) {
            // Handle movement input только если не заблокировано и не скалолазим
            inputDirection = KCC.TransformRotation * new Vector3(input.MoveDirection.x, 0f, input.MoveDirection.y);

            // Apply sprint multiplier if sprint button is pressed
            float currentMoveSpeed = input.Buttons.IsSet(EInputButton.Sprint) ? moveSpeed * sprintMultiplier : moveSpeed;
            inputDirection *= currentMoveSpeed;

            // Handle jump input
            if (input.Buttons.WasPressed(PreviousButtons, EInputButton.Jump) && KCC.IsGrounded) {
                jumpImpulse = jumpForce;
                JumpCount++;
            }
        }

        // Если скалолазим, позиционируем игрока под зацепкой
        if (IsClimbing && CurrentHold != null) {
            // Только сервер вычисляет и устанавливает позицию
            if (Object.HasStateAuthority) {
                ClimbingHold hold = CurrentHold.GetComponent<ClimbingHold>();
                if (hold != null) {
                    Vector3 holdPosition = hold.GetHoldPosition();
                    // Игрок висит под зацепкой на расстоянии вытянутых рук
                    float hangDistance = 1.5f; // Расстояние висения (длина рук)
                    Vector3 hangingPosition = holdPosition + Vector3.down * hangDistance;

                    // Обновляем сетевую позицию
                    ClimbingPosition = hangingPosition;
                    KCC.SetPosition(hangingPosition);
                }
            }
            else {
                // Клиенты используют синхронизированную позицию с сервера
                if (ClimbingPosition != Vector3.zero) {
                    KCC.SetPosition(ClimbingPosition);
                }
            }

            inputDirection = Vector3.zero; // Отключаем обычное движение

            // Во время висения прыжок работает по-другому
            if (input.Buttons.WasPressed(PreviousButtons, EInputButton.Jump)) {
                // Прыжок в направлении взгляда
                Vector3 jumpDirection = cameraHandle.forward;
                jumpDirection.y = 0.2f; // Небольшой подъем
                jumpDirection = jumpDirection.normalized;

                // Отпускаем зацепку и прыгаем
                StopClimbing();
                KCC.Move(jumpDirection * jumpForce * 0.8f, jumpForce * 0.5f);
                jumpImpulse = 0f; // Уже применили прыжок
            }
            else {
                jumpImpulse = 0f; // Отключаем обычные прыжки
            }
        }

        // Move the player
        //Vector3 finalVelocity = inputDirection * currentMoveSpeed;
        MovePlayer(inputDirection, jumpImpulse);

        // Refresh the camera
        RefreshCamera();

        // Update animator with sprint parameter
        if (animator != null) {
            //animator.SetBool("IsSprinting", input.Buttons.IsSet(EInputButton.Sprint));
        }
        // Handle climbing input
        bool isHoldingClimb = input.Buttons.IsSet(EInputButton.ClimbHold);
        bool justPressedClimb = input.Buttons.WasPressed(PreviousButtons, EInputButton.ClimbHold);
        bool justReleasedClimb = input.Buttons.WasReleased(PreviousButtons, EInputButton.ClimbHold);

        HandleClimbingInput(isHoldingClimb, justPressedClimb, justReleasedClimb, input);

        // Handle pickup and throw input (только если не скалолазим)
        if (!IsClimbing) {
            bool justPressedPickUp = input.Buttons.WasPressed(PreviousButtons, EInputButton.Use);
            if (justPressedPickUp) {
                if (CurrentItem == null) {
                    UseItem();
                }
                else {
                    DropItem();
                }
            }
        }

        // Handle item ability input
        bool justPressedItemAbility = input.Buttons.WasPressed(PreviousButtons, EInputButton.ItemAbility);
        if (justPressedItemAbility && CurrentItem != null) {
            UseItemAbility();
        }

        // Update jump count
        if (KCC.HasJumped) {
            JumpCount++;
        }
        PreviousButtons = input.Buttons;
    }
    #endregion


    #region Movement and Camera
    // Moves the player using the desired velocity and jump impulse.
    private void MovePlayer(Vector3 desiredMoveVelocity = default, float jumpImpulse = default) {
        if (KCC == null) {
            return;
        }

        float acceleration;
        if (desiredMoveVelocity == Vector3.zero) {
            acceleration = KCC.IsGrounded ? groundDeceleration : airDeceleration;
        }
        else {
            acceleration = KCC.IsGrounded ? groundAcceleration : airAcceleration;
        }

        MoveVelocity = Vector3.Lerp(MoveVelocity, desiredMoveVelocity, acceleration * Runner.DeltaTime);

        KCC.Move(MoveVelocity, jumpImpulse);
    }


    // Updates the camera rotation based on the player's look direction.
    private void RefreshCamera() {
        Vector2 pitchRotation = KCC.GetLookRotation(true, false);
        cameraHandle.localRotation = Quaternion.Euler(pitchRotation);
    }
    #endregion

    #region Use Actions
    private void UseItem() {
        InteractableItemBase item = FindLookedAtItem();
        if (item != null) {
            item.RequestToUse(Object.InputAuthority);
        }
    }
    private void UseItemAbility() {
        // Delegate ability activation to the held item.
        var pickupItem = CurrentItem.GetComponent<InteractableItemBase>();
        pickupItem?.ItemAbility(this);
    }
    private void DropItem() {
        if (CurrentItem != null) {
            CurrentItem.GetComponent<InteractableItemBase>().RequestDrop();
        }
    }
    #endregion


    #region Pickup/Dropd Logic
    // Modified method - replaces the existing FindLookedAtItem method
    public InteractableItemBase FindLookedAtItem() {
        Vector3 origin = cameraHandle.position;
        Vector3 direction = cameraHandle.forward;

        // Separate layer masks for obstacles and items
        int obstacleMask = LayerMask.GetMask(); // Only walls/environment for obstacles
        int itemMask = LayerMask.GetMask("InteractibleItem"); // Items we can pick up

        // Calculate adaptive pickup range by checking for obstacles
        float adaptiveRange = CalculateAdaptiveRange(origin, direction, obstacleMask);

        // Use SphereCastAll to get all hits and prioritize items
        RaycastHit[] hits = Physics.SphereCastAll(origin, pickupRadius, direction, adaptiveRange, itemMask, QueryTriggerInteraction.Ignore);

        // Find the closest valid item from all hits
        InteractableItemBase closestItem = null;
        float closestDistance = float.MaxValue;

        foreach (RaycastHit hit in hits) {
            InteractableItemBase candidate = hit.collider.GetComponentInParent<InteractableItemBase>();
            if (candidate != null && candidate.Object.IsValid && candidate.IsCanBeUse()) {
                float distance = hit.distance;
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestItem = candidate;
                }
            }
        }

        return closestItem;
    }

    // Calculate adaptive range based on obstacles in the path
    private float CalculateAdaptiveRange(Vector3 origin, Vector3 direction, int obstacleMask) {
        float maxRange = Mathf.Min(pickupRange, maxPickupRange);

        // Check for obstacles that would block the sphere cast (only walls/environment)
        if (Physics.SphereCast(origin, pickupRadius, direction, out RaycastHit obstacleHit, maxRange, obstacleMask, QueryTriggerInteraction.Ignore)) {
            // If we hit an obstacle, reduce the range to just before the obstacle
            float adaptiveRange = Mathf.Max(0.1f, obstacleHit.distance - pickupRadius * 0.1f);
            return Mathf.Min(adaptiveRange, maxRange);
        }

        // No obstacles found, use the full range
        return maxRange;
    }


    // Handles logic when the held item changes.
    public void OnItemChanged() {
        if (CurrentItem != null) {
            var item = CurrentItem.GetComponent<InteractableItemBase>();
            if (item != null) {
                // Attach the item to the player (third-person view)
                item.AttachToPlayer(this);

                // For first-person view (only for local player)
                if (HasInputAuthority) {
                    ActivateFPPModel(item.ItemID);
                }
            }
        }
        else {
            // If there is no item, deactivate the FPP model
            if (HasInputAuthority) {
                DeactivateCurrentFPP();
            }
        }
    }


    // Activates the first-person model in hands corresponding to the given item ID.
    private void ActivateFPPModel(int itemID) {
        // Deactivate the previous model first
        DeactivateCurrentFPP();

        // Find the FPP model by its item ID
        var fppModel = FindFPPModelByID(itemID);
        if (fppModel != null) {
            fppModel.GetComponent<ItemID>().itemVisual.SetActive(true);
            currentFPPModel = fppModel;
        }
    }

    // Deactivates the current first-person model in hands.
    private void DeactivateCurrentFPP() {
        if (currentFPPModel != null) {
            currentFPPModel.GetComponent<ItemID>().itemVisual.SetActive(false);
            currentFPPModel = null;
        }
    }

    // Finds a first-person model based on the item ID.
    private GameObject FindFPPModelByID(int itemID) {
        foreach (Transform child in firstPersonItemHolder) {
            var fppModel = child.GetComponent<ItemID>();
            if (fppModel != null && fppModel.itemID == itemID) {
                return child.gameObject;
            }
        }
        return null;
    }

    // Drops the current item when the player dies.
    public void DoDropOnDeath() {
        if (CurrentItem == null)
            return;

        var pickInHand = CurrentItem.GetComponent<InteractableItemBase>();
        if (pickInHand == null)
            return;

        // 1) Spawn the item on the ground
        if (pickInHand.sceneObject != null) {
            Vector3 dropPos = transform.position + transform.forward + Vector3.up;
            Runner.Spawn(pickInHand.sceneObject, dropPos, Quaternion.identity);
        }
        // 2) Disable the FPP model (drop from hands)
        DeactivateCurrentFPP();
        // 3) Despawn the networked item
        Runner.Despawn(CurrentItem);
        CurrentItem = null;
    }
    #endregion

    #region Special Effects
    public void FreezePlayer(float duration) {
        if (!Object.HasStateAuthority) return;

        IsFrozen = true;

        // Use DOTween for the timer (user preference)
        DOVirtual.DelayedCall(duration, () => {
            if (Object != null && Object.HasStateAuthority) {
                IsFrozen = false;
            }
        });
    }

    private void SetBlackScreenOverlay(bool showBlackScreen) {
        if (showBlackScreen) {
            // Modify camera culling to only render UI layers
            SetCameraCullingForBlindness(true);
        }
        else {
            // Restore normal camera culling
            SetCameraCullingForBlindness(false);
        }
    }

    private void SetCameraCullingForBlindness(bool isBlind) {
        // Find the main camera (first person camera)
        Camera mainCam = null;

        // Try to find camera through Cinemachine virtual cameras
        foreach (var vcam in GetComponentsInChildren<CinemachineVirtualCamera>(true)) {
            if (vcam.enabled) {
                // Get the brain that controls this virtual camera
                var brain = CinemachineCore.Instance.GetActiveBrain(0);
                if (brain != null) {
                    mainCam = brain.OutputCamera;
                    break;
                }
            }
        }

        // Fallback to Camera.main if no Cinemachine camera found
        if (mainCam == null) {
            mainCam = Camera.main;
        }

        if (mainCam != null) {
            if (isBlind) {
                // Store original culling mask and set to only render UI layer (layer 5)
                if (!mainCam.gameObject.TryGetComponent<BlindnessState>(out var blindnessState)) {
                    blindnessState = mainCam.gameObject.AddComponent<BlindnessState>();
                }
                blindnessState.originalCullingMask = mainCam.cullingMask;
                mainCam.cullingMask = 1 << 5; // Only render UI layer
            }
            else {
                // Restore original culling mask
                if (mainCam.gameObject.TryGetComponent<BlindnessState>(out var blindnessState)) {
                    mainCam.cullingMask = blindnessState.originalCullingMask;
                    Destroy(blindnessState);
                }
            }
        }
    }

    // Helper component to store original camera state
    private class BlindnessState : MonoBehaviour {
        public int originalCullingMask;
    }
    // Add this method to visualize pickup spheres in the editor

    #endregion

    #region Team Material Management
    private void ApplyTeamMaterial() {
        Material materialToApply = GetTeamMaterial();
        if (materialToApply == null || teamMaterialRenderer == null) return;

        // Apply material to the specific SkinnedMeshRenderer
        Material[] materials = new Material[teamMaterialRenderer.materials.Length];
        for (int i = 0; i < materials.Length; i++) {
            materials[i] = materialToApply;
        }
        teamMaterialRenderer.materials = materials;
    }

    private Material GetTeamMaterial() {
        // Get materials from DodgeBallGameMode if it exists
        if (DodgeBallGameMode.Instance != null) {
            return DodgeBallGameMode.Instance.GetMaterialForTeam(TeamId);
        }

        // Get materials from KnifeGameMode if it exists
        if (KnifeGameMode.Instance != null) {
            return KnifeGameMode.Instance.GetMaterialForTeam(TeamId);
        }

        // Fallback - return null for default behavior
        return null;
    }
    #endregion

    #region RPC Requests
    [Rpc(RpcSources.All, RpcTargets.InputAuthority)]
    public void RPC_ShowHitMarker() {
        UIGame.Instance?.PlayerView?.Crosshair?.ShowHit(true, false);
    }

    [Rpc(RpcSources.All, RpcTargets.InputAuthority)]
    public void RPC_ShowGotHit() {
        UIGame.Instance?.PlayerView?.GetHit();
    }

    [Rpc(RpcSources.InputAuthority, RpcTargets.StateAuthority, Channel = RpcChannel.Reliable)]
    private void RPC_SetPlayerNickname(PlayerRef playerRef, string nick) {
        if (GameManager.Instance.PlayerData.TryGet(playerRef, out var pd)) {
            pd.Nickname = nick;
            GameManager.Instance.PlayerData.Set(playerRef, pd);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_SetLeader(bool value) {
        IsLeader = value;
        if (leaderIndicator != null) {
            leaderIndicator.SetActive(value);
        }
    }

    [Rpc(RpcSources.All, RpcTargets.All)]
    public void RPC_PlayPlayerHitAnimation() {
        if (animator != null) {
            animator.SetTrigger("PlayerHit");
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_PlayMeleeAttackAnimation() {
        if (animator != null) {
            animator.SetTrigger("meleeAttack");
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_ApplyKnockback(Vector3 knockbackVelocity) {
        if (Object.HasStateAuthority) {
            // Apply knockback by modifying the MoveVelocity
            MoveVelocity += knockbackVelocity;
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_SetBlindState(bool isBlind) {
        if (HasInputAuthority) {
            // Create or remove black screen overlay
            SetBlackScreenOverlay(isBlind);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_SetTeamMaterial(int teamId) {
        TeamId = teamId;
        ApplyTeamMaterial();
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_SetDuckMask(bool isActive) {
        if (duckMask != null) {
            duckMask.SetActive(isActive);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_StartDancing(float duration) {
        if (Object.HasStateAuthority) {
            IsDancing = true;
            DanceTimer = TickTimer.CreateFromSeconds(Runner, duration);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    public void RPC_SetClimbingAnimation(bool isClimbing) {
        if (animator != null) {
            animator.SetBool("isClimbing", isClimbing);
        }
    }
    #endregion

    #region Debugging
    private void OnDrawGizmos() {
        if (cameraHandle == null) return;

        Vector3 origin = cameraHandle.position;
        Vector3 direction = cameraHandle.forward;
        int layerMask = LayerMask.GetMask("Default");

        // Calculate adaptive range for visualization
        float adaptiveRange = CalculateAdaptiveRange(origin, direction, layerMask);

        // Set gizmo color for pickup visualization
        Gizmos.color = Color.yellow;

        // Draw sphere at camera position (SphereCast origin)
        Gizmos.DrawWireSphere(origin, pickupRadius);

        // Draw sphere at the adaptive range end position
        Vector3 adaptiveEndPosition = origin + direction * adaptiveRange;
        Gizmos.DrawWireSphere(adaptiveEndPosition, pickupRadius);

        // Draw line to show the adaptive cast direction
        Gizmos.color = Color.green;
        Gizmos.DrawLine(origin, adaptiveEndPosition);

        // Draw max range in red if it's different from adaptive range
        float maxRange = Mathf.Min(pickupRange, maxPickupRange);
        if (adaptiveRange < maxRange) {
            Gizmos.color = Color.red;
            Vector3 maxEndPosition = origin + direction * maxRange;
            Gizmos.DrawWireSphere(maxEndPosition, pickupRadius * 0.5f);
            Gizmos.DrawLine(adaptiveEndPosition, maxEndPosition);
        }

        // Draw sphere around player for OverlapSphere visualization
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(transform.position, pickupRadius);
    }
    #endregion
}

using System.Collections.Generic;
using UnityEngine;
using Fusion;
using SimpleFPS;
using DG.Tweening;

/// <summary>
/// AssykGameMode manages the turn-based throwing mechanic where players try to knock prefabs out of a circle.
/// </summary>
public class AssykGameMode : NetworkBehaviour, IGameModeConfig {
    public static AssykGameMode Instance { get; private set; }

    #region Fields
    [Header("Gameplay Settings")]
    [SerializeField] private float lobbyDuration = 15f;
    [SerializeField] private float matchDuration = 120f;
    [SerializeField] private float playerRespawnTime = 5f;
    [SerializeField] private Transform loserSpawnPoint;

    [Header("Mode Description")]
    [TextArea]
    [SerializeField] private string modeDescription = "Введите описание режима Knife здесь";

    [Header("References")]
    public Transform circleCenter;
    public NetworkPrefabRef knockableAssyk;
    public GameManager gameManager;
    public NetworkPrefabRef assykInHandTPO;


    [Header("Configuration")]
    public float circleRadius = 5f;
    public int numberOfPrefabs = 15;
    public float prefabSpacing = 1.0f;
    #endregion

    #region Properties
    public float LobbyDuration => lobbyDuration;
    public float MatchDuration => matchDuration;
    public float PlayerRespawnTime => playerRespawnTime;
    public Transform LoserSpawnPoint => loserSpawnPoint;
    public string ModeDescription => modeDescription;
    #endregion

    #region Networked Fields
    [Networked] public PlayerRef CurrentTurnPlayer { get; private set; }
    [Networked, Capacity(50)] private NetworkLinkedList<NetworkObject> spawnedKnockableAssyks { get; }
    [Networked] private bool matchEnded { get; set; }
    [Networked] private int consecutiveHits { get; set; } // Track consecutive hits for current player
    #endregion


    #region Private Variables
    private bool hasRoundStarted = false;

    // We store the "snapshot" of prefabs (and their positions) at the beginning of the current player's throw.
    // Key: the prefab NetworkObject, Value: original position at turn start.
    private Dictionary<NetworkObject, Vector3> currentTurnKnockableAssyksPositions = new Dictionary<NetworkObject, Vector3>();
    #endregion

    #region Unity Lifecycle

    private void Awake() {

        if (Instance == null) {
            Instance = this;
        }
        else if (Instance != this) {
            Destroy(gameObject);
        }
        if (gameManager == null) {
            gameManager = GameManager.Instance;
        }
    }

    public override void Spawned() {
        if (!Object.HasStateAuthority) {
            enabled = false;
            return;
        }
    }
    #endregion

    #region Round Logic
    public void OnRoundStarted() {
        if (!Object.HasStateAuthority) {
            return;
        }
        if (hasRoundStarted) {
            return;
        }

        hasRoundStarted = true;
        matchEnded = false;

        // 1) Spawn the prefabs in a line
        SpawnKnockableAssyksInALine();

        // 2) Pick first player
        SelectFirstPlayerForTurn();
    }

    public void OnRoundEnded() {
        if (!Object.HasStateAuthority) {
            return;
        }
        matchEnded = true;
        DetermineWinnersAndLosers();
    }
    #endregion


    #region Winners & Losers Determination
    public void DetermineWinnersAndLosers() {
        int eligibleCount = 0;
        int minItems = int.MaxValue;

        // Count eligible players and find the minimum number of items collected
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Value.IsAlive) {
                eligibleCount++;
                minItems = Mathf.Min(minItems, kv.Value.AssykAmount);
            }
        }

        // Count eligible players with the minimum number of items
        int eligibleWithMin = 0;
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Value.IsAlive && kv.Value.AssykAmount == minItems) {
                eligibleWithMin++;
            }
        }

        // If all eligible players have the same number of items, do nothing
        if (eligibleWithMin == eligibleCount) {
            return;
        }

        // Mark players with the minimum number of items as losers
        foreach (var kv in gameManager.PlayerData) {
            var pd = kv.Value;
            if (pd.IsConnected && pd.IsAlive && pd.AssykAmount == minItems) {
                // Only set IsAlive = false if squid game mode is enabled
                if (gameManager.IsSquidGameMode) {
                    pd.IsAlive = false;
                }
                gameManager.PlayerData.Set(kv.Key, pd);
            }
        }
    }

    #endregion

    #region  Reset Mode State
    public void ResetModeState() {
        if (!Object.HasStateAuthority) return;

        spawnedKnockableAssyks.Clear();
        matchEnded = false;
        hasRoundStarted = false;
        CurrentTurnPlayer = PlayerRef.None;
        consecutiveHits = 0;
        currentTurnKnockableAssyksPositions.Clear();

        // Reset each player's assykCount
        foreach (var kvp in gameManager.PlayerData) {
            var pd = kvp.Value;
            pd.AssykAmount = 0;
            gameManager.PlayerData.Set(kvp.Key, pd);
        }
    }
    #endregion

    #region Specific Game Mode Rules
    private void SpawnKnockableAssyksInALine() {
        if (!Runner.IsServer) {
            return;
        }

        float totalLength = (numberOfPrefabs - 1) * prefabSpacing;
        Vector3 lineDir = circleCenter.right;
        Vector3 lineStart = circleCenter.position - lineDir * (totalLength / 2f);

        for (int i = 0; i < numberOfPrefabs; i++) {
            Vector3 spawnPos = lineStart + lineDir * (i * prefabSpacing);

            spawnPos.y += 0.1f;

            NetworkObject newObj = Runner.Spawn(knockableAssyk, spawnPos, Quaternion.Euler(90, 0, 0));
            spawnedKnockableAssyks.Add(newObj);
        }
    }
    private void SelectFirstPlayerForTurn() {
        if (!Object.HasStateAuthority) {
            return;
        }

        List<PlayerRef> alivePlayers = new List<PlayerRef>();

        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsAlive && kv.Value.IsConnected) {
                alivePlayers.Add(kv.Key);
            }
        }
        if (alivePlayers.Count > 0) {
            int randomIndex = Random.Range(0, alivePlayers.Count);
            BeginPlayerTurn(alivePlayers[randomIndex]);
        }
    }

    private void SwitchTurnToNextPlayer() {
        // If match already ended, do nothing
        if (matchEnded) {
            return;
        }

        // Reset consecutive hits when switching to next player
        consecutiveHits = 0;

        // Собираем список всех игроков, которые сейчас в игре
        List<PlayerRef> allPlayers = new List<PlayerRef>();
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected) {
                allPlayers.Add(kv.Key);
            }
        }
        allPlayers.Sort((a, b) => a.RawEncoded - b.RawEncoded);

        // find current player index
        int currentIndex = allPlayers.IndexOf(CurrentTurnPlayer);
        if (currentIndex < 0) {
            // fallback: if current player is not in the list, find the next non-loser
            if (allPlayers.Count > 0) {
                // find next non-loser starting from the first player
                PlayerRef nextNonLoser = FindNextNonLoser(allPlayers, 0);
                if (nextNonLoser != PlayerRef.None) {
                    CurrentTurnPlayer = nextNonLoser;
                    BeginPlayerTurn(CurrentTurnPlayer);
                }
            }
            return;
        }

        int nextIndex = (currentIndex + 1) % allPlayers.Count;
        PlayerRef nextPlayer = FindNextNonLoser(allPlayers, nextIndex);

        if (nextPlayer == PlayerRef.None) {
            //Debug.Log("SwitchTurnToNextPlayer: no non-loser players found.");
            return;
        }
        // Else, switch to next player
        CurrentTurnPlayer = nextPlayer;
        BeginPlayerTurn(CurrentTurnPlayer);
    }


    private void BeginPlayerTurn(PlayerRef playerRef) {
        //Debug.Log($"[DEBUG] BeginPlayerTurn: playerRef={playerRef}, matchEnded={matchEnded}");
        if (playerRef.IsNone || matchEnded) {
            return;
        }
        CurrentTurnPlayer = playerRef;

        // 1) Clear previous data
        currentTurnKnockableAssyksPositions.Clear();

        // 2) Record the position of each still-active prefab
        foreach (var prefab in spawnedKnockableAssyks) {
            if (prefab != null) {
                currentTurnKnockableAssyksPositions[prefab] = prefab.transform.position;
            }
        }

        // 3) Force the player to the throw position
        // ForcePlayerToThrowPosition(playerRef);

        // 4) Spawn the assyk item in hand
        SpawnThrowableAssykInHand(playerRef);
    }

    #endregion

    #region OnThrowMissed / OnSuccessfulThrow
    public void OnThrowMissed() {
        if (!Object.HasStateAuthority || matchEnded) return;

        // Reset consecutive hits counter on miss
        consecutiveHits = 0;

        // Restore prefabs to their original positions
        foreach (var kvp in currentTurnKnockableAssyksPositions) {
            NetworkObject prefab = kvp.Key;
            Vector3 originalPos = kvp.Value;

            if (prefab != null) {
                prefab.transform.position = originalPos;
            }
        }

        // If in single player mode, just repeat the turn
        if (IsSinglePlayer()) {
            BeginPlayerTurn(CurrentTurnPlayer);
        }
        else {
            // Several players left, switch to next
            SwitchTurnToNextPlayer();
        }
    }

    public void OnSuccessfulThrowContinues() {
        //Debug.Log($"[DEBUG] OnSuccessfulThrowContinues: CurrentTurnPlayer={CurrentTurnPlayer}");
        if (!Object.HasStateAuthority || matchEnded) return;
        BeginPlayerTurn(CurrentTurnPlayer);
    }
    #endregion

    #region When a prefab is actually knocked out
    public void OnKnockableAssykKnockedOut(NetworkObject knockableAssyk, PlayerRef scoringPlayer) {
        // Debug.Log($"[DEBUG] OnKnockableAssykKnockedOut: scoringPlayer={scoringPlayer}, CurrentTurnPlayer={CurrentTurnPlayer}, consecutiveHits={consecutiveHits}");
        if (!Object.HasStateAuthority || matchEnded) {
            return;
        }

        // Increase that player's assykCount
        if (gameManager.PlayerData.TryGet(scoringPlayer, out var pd)) {
            pd.AssykAmount += 1;
            pd.Money += 25;
            gameManager.PlayerData.Set(scoringPlayer, pd);
        }

        // Remove from spawnedPrefabs
        if (spawnedKnockableAssyks.Contains(knockableAssyk)) {
            spawnedKnockableAssyks.Remove(knockableAssyk);
        }

        // Also remove from currentTurnPrefabsPositions so we won't restore it on OnThrowMissed
        if (currentTurnKnockableAssyksPositions.ContainsKey(knockableAssyk)) {
            currentTurnKnockableAssyksPositions.Remove(knockableAssyk);
        }

        if (spawnedKnockableAssyks.Count == 0) {
            GameManager.Instance.EndMatch();
            return;
        }

        // Use DOTween instead of coroutine for despawn delay
        DOVirtual.DelayedCall(4f, () => {
            if (knockableAssyk != null && knockableAssyk.IsValid) {
                Runner.Despawn(knockableAssyk);
            }
        });

        // Increment consecutive hits counter
        consecutiveHits++;

        // Check if player has hit 2 times in a row
        if (consecutiveHits >= 2) {
            // Reset counter and switch to next player
            consecutiveHits = 0;

            // If in single player mode, just continue the turn
            if (IsSinglePlayer()) {
                BeginPlayerTurn(CurrentTurnPlayer);
            }
            else {
                // Several players left, switch to next
                SwitchTurnToNextPlayer();
            }
        }
        else {
            // Continue current player's turn
            OnSuccessfulThrowContinues();
        }
    }
    #endregion


    #region Helpers
    private PlayerRef FindNextNonLoser(List<PlayerRef> sortedPlayers, int startIndex) {
        if (sortedPlayers.Count == 0) return PlayerRef.None;

        int count = sortedPlayers.Count;
        int index = startIndex;
        for (int i = 0; i < count; i++) {
            PlayerRef candidate = sortedPlayers[index];
            var pd = gameManager.PlayerData[candidate];
            if (pd.IsAlive) {
                // Found a non-loser
                return candidate;
            }
            index = (index + 1) % count;
        }
        // No non-losers found
        return PlayerRef.None;
    }


    private void SpawnThrowableAssykInHand(PlayerRef playerRef) {
        // Debug.Log($"[DEBUG] SpawnThrowableAssykInHand: playerRef={playerRef}, assykInHandTPO.IsValid={assykInHandTPO.IsValid}");
        if (assykInHandTPO.IsValid && Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
            var pc = playerObj.GetComponent<PlayerController>();
            if (pc != null) {
                // Debug.Log($"[DEBUG] PlayerController found, current item: {pc.CurrentItem}");
                // Despawn old item if needed
                if (pc.CurrentItem != null) {
                    Runner.Despawn(pc.CurrentItem);
                    pc.CurrentItem = null;
                }

                // Spawn a new Assyk in-hand
                NetworkObject newItem = Runner.Spawn(assykInHandTPO, Vector3.zero, Quaternion.identity, playerRef);
                pc.CurrentItem = newItem;
                //Debug.Log($"[DEBUG] CurrentItem set to: {pc.CurrentItem}");
                //Debug.Log($"[DEBUG] New item spawned: {newItem}");
            }
            else {
                //Debug.LogError($"[DEBUG] PlayerController is NULL for player {playerRef}");
            }
        }
        else {
            //Debug.LogError($"[DEBUG] Failed to get player object or assykInHandTPO invalid. PlayerObj found: {Runner.TryGetPlayerObject(playerRef, out _)}, TPO valid: {assykInHandTPO.IsValid}");
        }
    }
    private bool IsSinglePlayer() {
        int connectedCount = 0;
        foreach (var kvp in gameManager.PlayerData) {
            if (kvp.Value.IsConnected) {
                connectedCount++;
                if (connectedCount > 1) return false;
            }
        }
        return (connectedCount == 1);
    }

    private void OnDrawGizmos() {
        if (circleCenter != null) {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(circleCenter.position, circleRadius);
        }
    }

    #endregion
}

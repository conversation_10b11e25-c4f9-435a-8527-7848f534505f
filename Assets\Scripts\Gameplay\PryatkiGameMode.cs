using UnityEngine;
using Fusion;
using SimpleFPS;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;


public class PryatkiGameMode : NetworkBehaviour, IGameModeConfig {
    public static PryatkiGameMode Instance { get; private set; }

    #region Fields
    [Header("Gameplay Settings")]
    [SerializeField] private float lobbyDuration = 30f;
    [SerializeField] private float matchDuration = 120f;
    [SerializeField] private float playerRespawnTime = 5f;
    [SerializeField] private float invisibilityDuration = 5f;
    [SerializeField] private float blindnessDuration = 3f;


    [Header("Mode Description")]
    [TextArea][SerializeField] private string modeDescription = "Enter game mode description here";

    [Header("References")]
    [SerializeField] private GameManager gameManager;
    [SerializeField] private Transform loserSpawnPoint;
    public NetworkPrefabRef itemPryatki;
    public PryatkiTrigger[] pryatkiZones;
    [SerializeField] private GameObject blindfoldPrefab;

    #endregion

    #region Networked Properties
    [Networked] public PlayerRef CurrentLeader { get; private set; }

    // Cache of any active chase, storing who was tagged by whom,so we can determine who reaches the zone first.
    [Networked, Capacity(2)] private NetworkDictionary<PlayerRef, PlayerRef> activeChases => default;
    #endregion

    #region Properties
    public float LobbyDuration => lobbyDuration;
    public float MatchDuration => matchDuration;
    public float PlayerRespawnTime => playerRespawnTime;
    public Transform LoserSpawnPoint => loserSpawnPoint;
    public string ModeDescription => modeDescription;
    #endregion

    #region Private Variables
    private List<PryatkiTrigger> activeZones = new List<PryatkiTrigger>();
    private Dictionary<PlayerRef, GameObject> playerBlindfolds = new Dictionary<PlayerRef, GameObject>();
    private bool matchAlreadyEnded = false;
    #endregion

    #region Unity Lifecycle
    private void Awake() {
        if (Instance == null) {
            Instance = this;
        }
        else if (Instance != this) {
            Destroy(gameObject);
        }
        if (gameManager == null) {
            gameManager = GameManager.Instance;
        }
    }

    public override void Spawned() {
        // We only allow the server (StateAuthority in Fusion) to execute game logic.
        if (!Object.HasStateAuthority) {
            enabled = false;
            return;
        }

        // Make sure we have a reference to this instance.
        if (Instance == null) {
            Instance = this;
        }

        InitializePryatkiZones();
    }

    public override void FixedUpdateNetwork() {
        if (!Object.HasStateAuthority || gameManager == null) return;

        // Only check during active match
        if (gameManager.State == EGameplayState.Match) {
            CheckForGameEnd();
        }
    }
    #endregion

    #region Round Logic
    public void OnRoundStarted() {
        if (!Object.HasStateAuthority) {
            return;
        }

        // If there is no leader, choose the first available alive player
        ChooseRandomLeader();

        // Clear any old chase states.
        activeChases.Clear();
    }
    public void OnRoundEnded() {
        if (!Object.HasStateAuthority) { return; }

        DetermineWinnersAndLosers();
    }
    #endregion

    #region Winners & Losers Determination
    public void DetermineWinnersAndLosers() {
        // If there's a valid leader, we can mark them as the loser.
        if (CurrentLeader != PlayerRef.None && gameManager.PlayerData.TryGet(CurrentLeader, out var pd)) {
            // Only set IsAlive = false if squid game mode is enabled
            if (gameManager.IsSquidGameMode) {
                pd.IsAlive = false;
            }
            gameManager.PlayerData.Set(CurrentLeader, pd);
        }

        // Award points to non-leader players (survivors)
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Key != CurrentLeader) {
                var playerData = kv.Value;
                playerData.Money += 100;
                gameManager.PlayerData.Set(kv.Key, playerData);
            }
        }

        // Check if game should end after leader elimination
        CheckForGameEnd();
    }

    private void CheckForGameEnd() {
        if (!Object.HasStateAuthority || matchAlreadyEnded) return;

        // Count alive players
        List<PlayerRef> alivePlayers = new List<PlayerRef>();
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsAlive && kv.Value.IsConnected) {
                alivePlayers.Add(kv.Key);
            }
        }

        // If only one player remains, end the match immediately (they win)
        if (alivePlayers.Count <= 1) {
            matchAlreadyEnded = true;
            gameManager.EndMatch();
        }
    }
    #endregion

    #region Reset Mode State
    public void ResetModeState() {
        if (!Object.HasStateAuthority) { return; }

        // Reset match state
        matchAlreadyEnded = false;

        // Remove the current leader.
        if (CurrentLeader != PlayerRef.None) {
            SetLeader(PlayerRef.None);
        }

        activeChases.Clear();

        if (Runner != null && gameManager != null && gameManager.PlayerData.Count > 0) {
            foreach (var kvp in gameManager.PlayerData) {
                PlayerRef pRef = kvp.Key;
                if (Runner.TryGetPlayerObject(pRef, out NetworkObject playerObj)) {
                    var pc = playerObj.GetComponent<PlayerController>();
                    if (pc != null && pc.CurrentItem != null) {
                        Runner.Despawn(pc.CurrentItem);
                        pc.CurrentItem = null;
                    }
                }
            }
        }
    }
    #endregion


    #region Specific Game Mode Rules
    private void ChooseRandomLeader() {
        if (!Object.HasStateAuthority) {
            return;
        }

        List<PlayerRef> alivePlayers = new List<PlayerRef>();

        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsAlive && kv.Value.IsConnected) {
                alivePlayers.Add(kv.Key);
            }
        }

        if (alivePlayers.Count > 0) {
            int randomIndex = Random.Range(0, alivePlayers.Count);
            SetLeaderInitially(alivePlayers[randomIndex]);
        }
    }
    public void SetLeaderInitially(PlayerRef newLeader) {
        if (!Object.HasStateAuthority) {
            return;
        }

        // 1) Remove the previous leader if present and different from the new leader
        if (CurrentLeader != PlayerRef.None && CurrentLeader != newLeader) {
            // Remove the leader indicator from the old leader
            if (Runner.TryGetPlayerObject(CurrentLeader, out var oldLeaderObj)) {
                var oldLeaderPC = oldLeaderObj.GetComponent<PlayerController>();
                oldLeaderPC?.RPC_SetLeader(false);

                // If the old leader had a PryatkiItem, despawn it
                if (oldLeaderPC != null && oldLeaderPC.CurrentItem != null) {
                    var oldItem = oldLeaderPC.CurrentItem.GetComponent<LaserItem>();
                    if (oldItem != null) {
                        Runner.Despawn(oldLeaderPC.CurrentItem);
                        oldLeaderPC.CurrentItem = null;
                        oldLeaderPC.OnItemChanged();
                    }
                }
            }
        }

        // 2) New leader
        CurrentLeader = newLeader;

        if (newLeader != PlayerRef.None && Runner.TryGetPlayerObject(newLeader, out var newLeaderObj)) {
            var newLeaderPC = newLeaderObj.GetComponent<PlayerController>();
            newLeaderPC?.RPC_SetLeader(true);
            // 4) Apply temporary blindness
            ApplyTemporaryBlindness(newLeader);
            // 3) Spawn the PryatkiItem in the leader's hand
            SpawnPryatkiItemInHand(newLeader);
        }
    }
    public void SetLeader(PlayerRef newLeader) {
        if (!Object.HasStateAuthority) {
            return;
        }

        // 1) Remove the previous leader if present and different from the new leader
        if (CurrentLeader != PlayerRef.None && CurrentLeader != newLeader) {
            // Remove the leader indicator from the old leader
            if (Runner.TryGetPlayerObject(CurrentLeader, out var oldLeaderObj)) {
                var oldLeaderPC = oldLeaderObj.GetComponent<PlayerController>();
                oldLeaderPC?.RPC_SetLeader(false);

                // If the old leader had a PryatkiItem, despawn it
                if (oldLeaderPC != null && oldLeaderPC.CurrentItem != null) {
                    var oldItem = oldLeaderPC.CurrentItem.GetComponent<LaserItem>();
                    if (oldItem != null) {
                        Runner.Despawn(oldLeaderPC.CurrentItem);
                        oldLeaderPC.CurrentItem = null;
                        oldLeaderPC.OnItemChanged();
                    }
                }
            }
        }

        // 2) New leader
        CurrentLeader = newLeader;

        if (newLeader != PlayerRef.None && Runner.TryGetPlayerObject(newLeader, out var newLeaderObj)) {
            var newLeaderPC = newLeaderObj.GetComponent<PlayerController>();
            newLeaderPC?.RPC_SetLeader(true);

            // 3) Spawn the PryatkiItem in the leader's hand
            SpawnPryatkiItemInHand(newLeader);

            // 4) Do not Apply temporary blindness
        }
    }
    private void SpawnPryatkiItemInHand(PlayerRef playerRef) {
        if (CurrentLeader == PlayerRef.None) { return; }
        if (playerRef.IsNone) { return; }

        var pd = GameManager.Instance.PlayerData[playerRef];

        if (!pd.IsAlive) {
            return;
        }

        if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
            PlayerController pc = playerObj.GetComponent<PlayerController>();
            if (pc == null) {
                return;
            }
            Transform spawnHolder = pc.thirdPersonItemHolder;
            Vector3 spawnPos = (spawnHolder != null) ? spawnHolder.position : playerObj.transform.position;
            Quaternion spawnRot = (spawnHolder != null) ? spawnHolder.rotation : Quaternion.identity;

            NetworkObject newItem = Runner.Spawn(itemPryatki, spawnPos, spawnRot, playerRef);
            if (newItem == null) {
                return;
            }
            if (spawnHolder != null) {
                newItem.transform.SetParent(spawnHolder, false);
            }
            pc.CurrentItem = newItem;
        }
    }
    // Called when the leader "tags" (spots) another player and triggers a chase to the zone.
    // We store the chase in a dictionary so we can check who arrives first in OnZoneTriggered.
    public void OnPlayerTagged(PlayerRef leader, PlayerRef target) {
        if (!Object.HasStateAuthority) {
            return;
        }

        if (leader == target) {
            return;
        }

        if (activeChases.ContainsKey(leader)) {
            activeChases.Remove(leader);
        }
        activeChases.Add(leader, target);

        HideLeaderItem(leader);

        EnableRandomZones(1);

        // Notify UI about the tagging
        RPC_NotifyPlayerTagged(leader, target);
    }

    public void OnPlayerEnteredZone(PlayerRef who) {

        if (!Object.HasStateAuthority) {
            return;
        }

        List<PlayerRef> leaderKeysToRemove = new List<PlayerRef>();

        foreach (var chase in activeChases) {
            PlayerRef leader = chase.Key;
            PlayerRef victim = chase.Value;

            if (who == leader) {
                SetPlayerInvisible(leader);
                // Restore leader's item before leadership change
                RestoreLeaderItem(leader);
                SetLeader(victim);  // The leader arrived first => the "victim" becomes the new leader
                leaderKeysToRemove.Add(leader);    // We break because each leader can have only one chase at a time
                DisableAllZones();
            }
            else if (who == victim) {
                SetPlayerInvisible(victim);
                // Restore leader's item since chase is over
                RestoreLeaderItem(leader);
                leaderKeysToRemove.Add(leader);
                DisableAllZones();
                // The victim arrived first => the leader stays leader, do nothing except remove the chase
            }
        }

        // Remove all completed chases from the dictionary
        foreach (var key in leaderKeysToRemove) {
            activeChases.Remove(key);
        }

        // Notify UI about zone entry
        RPC_NotifyPlayerEnteredZone(who);
    }
    private void SetPlayerInvisible(PlayerRef playerRef) {
        if (Object.HasStateAuthority) {
            RPC_SetPlayerInvisible(playerRef);
        }
    }

    private void HideLeaderItem(PlayerRef leader) {
        if (Runner.TryGetPlayerObject(leader, out var leaderObj)) {
            var leaderPC = leaderObj.GetComponent<PlayerController>();
            if (leaderPC != null && leaderPC.CurrentItem != null) {
                // Despawn the item completely
                Runner.Despawn(leaderPC.CurrentItem);
                leaderPC.CurrentItem = null;
                leaderPC.OnItemChanged();
            }
        }
    }

    private void RestoreLeaderItem(PlayerRef leader) {
        if (Runner.TryGetPlayerObject(leader, out var leaderObj)) {
            var leaderPC = leaderObj.GetComponent<PlayerController>();
            if (leaderPC != null && leaderPC.CurrentItem == null) {
                // Respawn the PryatkiItem in leader's hand
                SpawnPryatkiItemInHand(leader);
            }
        }
    }
    #endregion

    #region Blindness Effects
    private void ApplyTemporaryBlindness(PlayerRef playerRef) {
        if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
            var playerController = playerObj.GetComponent<PlayerController>();
            if (playerController != null) {
                // Apply blindness
                playerController.RPC_SetBlindState(true);

                // Add blindfold visual
                AddBlindfold(playerRef);

                // Use DOTween instead of coroutine to remove blindness after duration
                DOVirtual.DelayedCall(blindnessDuration, () => {
                    if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
                        var playerController = playerObj.GetComponent<PlayerController>();
                        if (playerController != null) {
                            // Remove blindness
                            playerController.RPC_SetBlindState(false);

                            // Remove blindfold visual
                            RemoveBlindfold(playerRef);
                        }
                    }
                });
            }
        }
    }



    private void AddBlindfold(PlayerRef playerRef) {
        if (Object.HasStateAuthority) {
            RPC_AddBlindfold(playerRef);
        }
    }

    private void RemoveBlindfold(PlayerRef playerRef) {
        if (Object.HasStateAuthority) {
            RPC_RemoveBlindfold(playerRef);
        }
    }

    private void RemoveAllBlindfolds() {
        if (Object.HasStateAuthority) {
            RPC_RemoveAllBlindfolds();
        }
    }

    private Transform FindHeadBone(Transform root) {
        // Try to find head bone in common locations
        Transform[] bones = root.GetComponentsInChildren<Transform>();
        foreach (Transform bone in bones) {
            if (bone.name.ToLower().Contains("head") ||
                bone.name.ToLower().Contains("skull") ||
                bone.name.ToLower().Contains("cranium")) {
                return bone;
            }
        }
        return null;
    }
    #endregion

    #region Pryatki Zones
    private void InitializePryatkiZones() {
        if (pryatkiZones == null) { return; }

        foreach (var zone in pryatkiZones) {
            if (zone != null) {
                zone.gameObject.SetActive(false);
            }
        }
        activeZones.Clear();

        // Sync with clients
        SyncZonesToClients();
    }

    private void EnableRandomZones(int count) {
        // Disable all zones just in case
        DisableAllZones();

        if (pryatkiZones == null || pryatkiZones.Length == 0) return;
        // Randomly shuffle zones and take required amount
        var shuffled = pryatkiZones.OrderBy(x => Random.value).ToList();
        var chosen = shuffled.Take(count);

        foreach (var zone in chosen) {
            zone.gameObject.SetActive(true);
            activeZones.Add(zone);
        }

        // After enabling - sync with clients
        SyncZonesToClients();
    }
    private void DisableAllZones() {
        activeZones.Clear();
        if (pryatkiZones == null) { return; }

        foreach (var zone in pryatkiZones) {
            zone.gameObject.SetActive(false);
        }

        // After disabling - sync with clients
        SyncZonesToClients();
    }
    private void SyncZonesToClients() {
        bool[] states = new bool[pryatkiZones.Length];

        for (int i = 0; i < pryatkiZones.Length; i++) {
            states[i] = pryatkiZones[i].gameObject.activeSelf;
        }

        RPC_SetZonesActive(states);
    }
    #endregion


    #region RPCs
    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_SetZonesActive(bool[] states) {
        // Just in case, check the length
        if (states == null || states.Length != pryatkiZones.Length) return;

        // Repeat SetActive on each client's side
        for (int i = 0; i < pryatkiZones.Length; i++) {
            if (pryatkiZones[i] != null) {
                pryatkiZones[i].gameObject.SetActive(states[i]);
            }
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_NotifyPlayerTagged(PlayerRef leader, PlayerRef target) {
        // Find UIGameplayInfo and notify about tagging
        var gameplayInfo = FindObjectOfType<UIGameplayInfo>();
        if (gameplayInfo != null) {
            gameplayInfo.OnPlayerTaggedUI(leader, target);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_NotifyPlayerEnteredZone(PlayerRef who) {
        // Find UIGameplayInfo and notify about zone entry
        var gameplayInfo = FindObjectOfType<UIGameplayInfo>();
        if (gameplayInfo != null) {
            gameplayInfo.OnPlayerEnteredZoneUI(who);
        }
    }
    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_RemoveBlindfold(PlayerRef playerRef) {
        if (playerBlindfolds.TryGetValue(playerRef, out GameObject blindfold)) {
            if (blindfold != null) {
                Destroy(blindfold);
            }
            playerBlindfolds.Remove(playerRef);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_AddBlindfold(PlayerRef playerRef) {
        if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
            PlayerController player = playerObj.GetComponent<PlayerController>();
            if (player != null && blindfoldPrefab != null && !playerBlindfolds.ContainsKey(playerRef)) {
                // Find the head bone in the player's skeleton
                Transform headBone = FindHeadBone(player.thirdPersonRoot.transform);

                if (headBone != null) {
                    // Use exact original rotation from prefab with offset
                    Vector3 localOffset = new Vector3(0, 0.3f, -0.01f);
                    Vector3 blindfoldPosition = headBone.position + localOffset;
                    GameObject blindfold = Instantiate(blindfoldPrefab, blindfoldPosition, blindfoldPrefab.transform.rotation, headBone);
                    playerBlindfolds[playerRef] = blindfold;
                }
                else {
                    // Fallback: use world position if head bone not found
                    Vector3 headPosition = player.cameraHandle.position;
                    Vector3 blindfoldOffset = new Vector3(0, 0.3f, -0.01f);
                    Vector3 blindfoldPosition = headPosition + blindfoldOffset;
                    Quaternion blindfoldRotation = blindfoldPrefab.transform.rotation;

                    GameObject blindfold = Instantiate(blindfoldPrefab, blindfoldPosition, blindfoldRotation, player.thirdPersonRoot.transform);
                    playerBlindfolds[playerRef] = blindfold;
                }
            }
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_RemoveAllBlindfolds() {
        foreach (var kv in playerBlindfolds) {
            if (kv.Value != null) {
                Destroy(kv.Value);
            }
        }
        playerBlindfolds.Clear();
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_SetPlayerInvisible(PlayerRef playerRef) {
        if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
            var playerController = playerObj.GetComponent<PlayerController>();
            if (playerController != null && playerController.thirdPersonRoot != null) {
                // Make player invisible
                playerController.thirdPersonRoot.SetActive(false);

                // Use DOTween to restore visibility after duration
                DOVirtual.DelayedCall(invisibilityDuration, () => {
                    if (playerController != null && playerController.thirdPersonRoot != null) {
                        playerController.thirdPersonRoot.SetActive(true);
                    }
                });
            }
        }
    }
    #endregion
}

﻿using TMPro;
using UnityEngine;
using Fusion;
using UnityEngine.UI;

namespace SimpleFPS {
    /// <summary>
    /// Main gameplay info panel at the top of the screen.
    /// Also handles displaying of announcements (e.g. Lead Lost, Lead Taken)
    /// </summary>
    public class UIGameplayInfo : MonoBehaviour {
        [Header("Setup")]
        public TextMeshProUGUI Position;
        public TextMeshProUGUI RemainingTime;
        public TextMeshProUGUI Kills;
        public TextMeshProUGUI GameModeDescriptionText;
        public Image TutorialImage1;
        public Image TutorialImage2;
        public Image TutorialImage3;
        public GameObject Skirmish;

        [Header("Announcer")]
        public GameObject GameplayStart;
        public GameObject LeadTaken;
        public GameObject LeadLost;
        public GameObject DoubleDamage;
        public GameObject GameModeRulesGO;

        [Header("CapsUI")]
        public GameObject capsViewer;
        public TextMeshProUGUI capsAmount;

        [Header("AssykUI")]
        public GameObject assykViewer;
        public TextMeshProUGUI assykAmount;

        [Header("MoneyUI")]
        public GameObject moneyViewer;
        public TextMeshProUGUI moneyAmount;

        [Header("TeamUI")]
        public GameObject BlueTeam;
        public GameObject RedTeam;

        [Header("LeaderIndicator")]
        public GameObject leaderIndicator;

        [Header("RunIndicator")]
        public GameObject runIndicator;

        [Header("Hide&SeekUI")]
        public GameObject HideTeam;
        public GameObject SeekerTeam;

        private UIGame gameUI;

        private int lastTime = -1;
        private int lastPosition;

        // Run indicator state tracking
        private bool isRunIndicatorActive = false;
        private PlayerRef currentChaseLeader = PlayerRef.None;
        private PlayerRef currentChaseTarget = PlayerRef.None;

        // Game mode tracking to detect changes
        private ESelectedGameMode lastGameMode = ESelectedGameMode.Lobby;

        private void Awake() {
            // Make sure to turn all announcements off on start.
            GameplayStart.SetActive(false);
            LeadTaken.SetActive(false);
            LeadLost.SetActive(false);
            DoubleDamage.SetActive(false);

            gameUI = GetComponentInParent<UIGame>();
        }

        // Refactor THIS!
        private void Update() {
            if (gameUI.Runner == null) {
                return;
            }

            var gameplay = gameUI.GameManager;

            if (gameplay.Object == null || gameplay.Object.IsValid == false) {
                return;
            }

            // Проверяем изменение игрового режима и обновляем UI
            if (gameplay.selectedGameMode != lastGameMode) {
                lastGameMode = gameplay.selectedGameMode;
                UpdateTutorialAndDescription(gameplay);
            }

            if (gameUI.Runner.TryGetPlayerObject(gameUI.Runner.LocalPlayer, out var localPlayerObj)) {
                var localPlayerController = localPlayerObj.GetComponent<PlayerController>();
                if (localPlayerController != null) {
                    // Handle leader and run indicators for Pryatki mode
                    bool isPryatkiMode = gameplay.selectedGameMode == ESelectedGameMode.Pryatki;

                    if (isPryatkiMode) {
                        // In Pryatki mode, manage leader and run indicators based on chase state
                        UpdatePryatkiIndicators(localPlayerController);
                    }
                    else {
                        // Hide leader indicator in AltPryatki mode, show in other modes
                        bool showLeaderIndicator = localPlayerController.IsLeader &&
                                                 gameplay.selectedGameMode != ESelectedGameMode.AltPryatki;
                        leaderIndicator.SetActive(showLeaderIndicator);
                        runIndicator.SetActive(false);

                    }

                    // Show team indicators for DodgeBall and Knife modes
                    bool isDodgeBallMode = gameplay.selectedGameMode == ESelectedGameMode.DodgeBall;
                    bool isKnifeModeForTeams = gameplay.selectedGameMode == ESelectedGameMode.Knife;

                    // Get player alive status
                    bool isPlayerAlive = gameplay.PlayerData.TryGet(gameUI.Runner.LocalPlayer, out PlayerData playerData) && playerData.IsAlive;

                    BlueTeam.SetActive((isDodgeBallMode || isKnifeModeForTeams) && localPlayerController.TeamId == 1 && isPlayerAlive);
                    RedTeam.SetActive((isDodgeBallMode || isKnifeModeForTeams) && localPlayerController.TeamId == 2 && isPlayerAlive);

                    bool isHideAndSeekMode = gameplay.selectedGameMode == ESelectedGameMode.AltPryatki;
                    HideTeam.SetActive(isHideAndSeekMode && !localPlayerController.IsLeader && isPlayerAlive);
                    SeekerTeam.SetActive(isHideAndSeekMode && localPlayerController.IsLeader && isPlayerAlive);
                }
            }

            if (gameplay.PlayerData.TryGet(gameUI.Runner.LocalPlayer, out PlayerData localPlayerData)) {

                bool isAssykGameMode = (gameplay.selectedGameMode == ESelectedGameMode.Assyk)
                                    && (localPlayerData.IsAlive);
                assykViewer.SetActive(isAssykGameMode);
                if (isAssykGameMode) {
                    assykAmount.text = localPlayerData.AssykAmount.ToString();
                }

                bool isLobbyScene = (gameplay.selectedGameMode == ESelectedGameMode.Lobby);
                bool isAltPryatkiMode = (gameplay.selectedGameMode == ESelectedGameMode.AltPryatki);
                //&& (localPlayerData.IsAlive);
                bool isDodgeBallMode = (gameplay.selectedGameMode == ESelectedGameMode.DodgeBall);
                //&& (localPlayerData.IsAlive);
                bool isKnifeMode = (gameplay.selectedGameMode == ESelectedGameMode.Knife);
                //&& (localPlayerData.IsAlive);

                /*moneyViewer.SetActive((isLobbyScene || isAltPryatkiMode || isDodgeBallMode || isKnifeMode) && localPlayerData.IsAlive);
                if ((isLobbyScene || isAltPryatkiMode || isDodgeBallMode || isKnifeMode) && localPlayerData.IsAlive) {
                    moneyAmount.text = localPlayerData.Money.ToString();
                }*/
                moneyViewer.SetActive(true);
                moneyAmount.text = localPlayerData.Money.ToString();

            }


            // 1) Hide timer if game is in GameOver state
            bool isGameOver = (gameplay.State == EGameplayState.GameOver || gameplay.State == EGameplayState.EndMatch);
            RemainingTime.gameObject.SetActive(!isGameOver);

            // 2) Show "Skirmish" text in lobby
            Skirmish.SetActive(gameplay.State == EGameplayState.PreMatch);

            GameModeRulesGO.SetActive(gameplay.State == EGameplayState.PreMatch && gameplay.selectedGameMode != null);

            // 3) Show "GameplayStart" text only during the round
            GameplayStart.SetActive(gameplay.State == EGameplayState.Match);

            // 4) Get remaining time (as integer) from TickTimer
            int remainingSeconds = (int)gameplay.RemainingTime.RemainingTime(gameUI.Runner).GetValueOrDefault();

            // 5) Update timer text (mm:ss)
            if (!isGameOver) {
                int minutes = remainingSeconds / 60;
                int seconds = remainingSeconds % 60;
                RemainingTime.text = $"{minutes}:{seconds:00}";
            }

            // 6) If we have local PlayerData, show Position, Kills, etc.
            if (gameplay.PlayerData.TryGet(gameUI.Runner.LocalPlayer, out PlayerData pd)) {
                ShowPlayerData(pd);
            }
        }

        private void ShowPlayerData(PlayerData playerData) {
            // Position
            if (playerData.StatisticPosition < int.MaxValue) {
                Position.text = $"#{playerData.StatisticPosition}";
            }
            else {
                Position.text = "-";
            }
            // Kills
            Kills.text = playerData.Kills.ToString();

            // Announcements
            if (playerData.StatisticPosition != lastPosition) {
                // Restart position animation
                Position.gameObject.SetActive(false);
                Position.gameObject.SetActive(true);

                if (playerData.StatisticPosition == 1 && playerData.Kills > 0) {
                    LeadTaken.SetActive(false);
                    LeadTaken.SetActive(true);
                }

                if (lastPosition == 1) {
                    LeadLost.SetActive(false);
                    LeadLost.SetActive(true);
                }

                lastPosition = playerData.StatisticPosition;
            }
        }
        public void SetGameModeDescription(string description) {
            if (GameModeDescriptionText != null) {
                GameModeDescriptionText.text = description;
            }
        }

        public void LoadTutorialImages(ESelectedGameMode gameMode) {
            string gameModeName = gameMode.ToString();

            // Загружаем изображения из Resources/Tutorial/GameModeName/
            Sprite tutorialSprite1 = Resources.Load<Sprite>($"Tutorial/{gameModeName}/1");
            Sprite tutorialSprite2 = Resources.Load<Sprite>($"Tutorial/{gameModeName}/2");
            Sprite tutorialSprite3 = Resources.Load<Sprite>($"Tutorial/{gameModeName}/3");

            // Присваиваем спрайты соответствующим Image компонентам
            if (TutorialImage1 != null && tutorialSprite1 != null) {
                TutorialImage1.sprite = tutorialSprite1;
            }

            if (TutorialImage2 != null && tutorialSprite2 != null) {
                TutorialImage2.sprite = tutorialSprite2;
            }

            if (TutorialImage3 != null && tutorialSprite3 != null) {
                TutorialImage3.sprite = tutorialSprite3;
            }
        }

        private void UpdateTutorialAndDescription(GameManager gameplay) {
            // Если это лобби, очищаем tutorial изображения
            if (gameplay.selectedGameMode == ESelectedGameMode.Lobby) {
                ClearTutorialImages();
                SetGameModeDescription("");
                return;
            }

            // Обновляем tutorial изображения
            LoadTutorialImages(gameplay.selectedGameMode);

            // Обновляем описание игрового режима
            string modeDescription = GetCurrentGameModeDescription(gameplay.selectedGameMode);
            if (!string.IsNullOrEmpty(modeDescription)) {
                SetGameModeDescription(modeDescription);
            }
        }

        private void ClearTutorialImages() {
            if (TutorialImage1 != null) {
                TutorialImage1.sprite = null;
            }
            if (TutorialImage2 != null) {
                TutorialImage2.sprite = null;
            }
            if (TutorialImage3 != null) {
                TutorialImage3.sprite = null;
            }
        }

        private string GetCurrentGameModeDescription(ESelectedGameMode gameMode) {
            switch (gameMode) {
                case ESelectedGameMode.Sifa:
                    return SifaGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.Knife:
                    return KnifeGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.Pryatki:
                    return PryatkiGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.Assyk:
                    return AssykGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.Klassiki:
                    return KlassikiGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.Zhmurki:
                    return ZhmurkiGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.Caps:
                    return CapsGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.AltPryatki:
                    return AltPryatkiGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.DodgeBall:
                    return DodgeBallGameMode.Instance?.ModeDescription ?? "";
                case ESelectedGameMode.Bottle:
                    return BottleGameMode.Instance?.ModeDescription ?? "";
                default:
                    return "";
            }
        }

        private void UpdatePryatkiIndicators(PlayerController localPlayerController) {
            // In Pryatki mode, show leader indicator normally when not in chase
            // Hide leader indicator and show run indicator when in chase
            if (isRunIndicatorActive) {
                leaderIndicator.SetActive(false);
                runIndicator.SetActive(true);
            }
            else {
                leaderIndicator.SetActive(localPlayerController.IsLeader);
                runIndicator.SetActive(false);
            }
        }

        // Called by PryatkiGameMode when a player is tagged
        public void OnPlayerTaggedUI(PlayerRef leader, PlayerRef target) {
            if (gameUI.Runner == null) return;

            PlayerRef localPlayer = gameUI.Runner.LocalPlayer;

            // Store current chase participants
            currentChaseLeader = leader;
            currentChaseTarget = target;

            // Show run indicator for both leader and target
            if (localPlayer == leader || localPlayer == target) {
                isRunIndicatorActive = true;
            }
        }

        // Called by PryatkiGameMode when a player enters zone
        public void OnPlayerEnteredZoneUI(PlayerRef who) {
            if (gameUI.Runner == null) return;

            PlayerRef localPlayer = gameUI.Runner.LocalPlayer;

            // If the player who entered zone is part of current chase, end the chase for all participants
            if (who == currentChaseLeader || who == currentChaseTarget) {
                // End chase for local player if they were part of it
                if (localPlayer == currentChaseLeader || localPlayer == currentChaseTarget) {
                    isRunIndicatorActive = false;
                }

                // Clear chase participants
                currentChaseLeader = PlayerRef.None;
                currentChaseTarget = PlayerRef.None;
            }
        }
    }
}

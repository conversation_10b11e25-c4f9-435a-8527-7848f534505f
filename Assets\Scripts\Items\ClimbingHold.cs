using UnityEngine;
using Fusion;
using SimpleFPS;

/// <summary>
/// Зацепка для скалолазания. Игрок может захватиться за неё, удерживая клавишу E.
/// </summary>
public class ClimbingHold : InteractableItemBase {
    [Header("Climbing Settings")]
    public float grabRadius = 2f; // Радиус захвата зацепки
    public Transform holdPoint; // Точка, к которой привязывается игрок
    public bool showGizmos = true; // Показывать ли гизмо в редакторе

    [Header("Visual Feedback")]
    public GameObject highlightEffect; // Эффект подсветки при возможности захвата

    // Networked свойства
    [Networked] public bool IsOccupied { get; set; } = false; // Занята ли зацепка
    [Networked] public PlayerRef CurrentClimber { get; set; } = PlayerRef.None; // Кто висит на зацепке

    private PlayerController nearbyPlayer; // Игрок рядом с зацепкой
    private bool wasPlayerNearby = false; // Для отслеживания изменений

    // Оптимизация: кэш игроков и ограничение частоты проверок
    private static PlayerController[] cachedPlayers;
    private static float lastPlayersCacheTime;
    private const float PLAYERS_CACHE_DURATION = 1f; // Обновляем кэш игроков каждую секунду
    private float lastNearbyCheckTime;
    private const float NEARBY_CHECK_INTERVAL = 0.1f; // Проверяем близость каждые 100мс

    public override void Spawned() {
        base.Spawned();

        // Если точка захвата не задана, используем центр объекта
        if (holdPoint == null) {
            holdPoint = transform;
        }

        // Изначально зацепка доступна
        IsAvailable = true;

        // Скрываем эффект подсветки
        if (highlightEffect != null) {
            highlightEffect.SetActive(false);
        }


    }

    public override void FixedUpdateNetwork() {
        if (!Object.HasStateAuthority) return;

        // Проверяем наличие игроков рядом с зацепкой (с ограничением частоты)
        float currentTime = Time.time;
        if (currentTime - lastNearbyCheckTime >= NEARBY_CHECK_INTERVAL) {
            CheckForNearbyPlayers();
            lastNearbyCheckTime = currentTime;
        }

        // Дополнительная проверка: если зацепка занята, но игрок не скалолазит - освобождаем
        if (IsOccupied && CurrentClimber != PlayerRef.None) {
            PlayerController climber = Runner.GetPlayerObject(CurrentClimber)?.GetComponent<PlayerController>();
            if (climber == null || !climber.IsClimbing || climber.CurrentHold != Object) {
                // Игрок не скалолазит или скалолазит на другой зацепке - освобождаем эту
                IsOccupied = false;
                CurrentClimber = PlayerRef.None;
            }
        }
    }

    public override void Render() {
        // Обновляем визуальную обратную связь
        UpdateVisualFeedback();
    }

    /// <summary>
    /// Получить кэшированный список игроков
    /// </summary>
    private static PlayerController[] GetCachedPlayers() {
        float currentTime = Time.time;

        // Обновляем кэш если прошло достаточно времени или кэш пустой
        if (cachedPlayers == null || currentTime - lastPlayersCacheTime > PLAYERS_CACHE_DURATION) {
            cachedPlayers = FindObjectsOfType<PlayerController>();
            lastPlayersCacheTime = currentTime;
        }

        return cachedPlayers;
    }

    /// <summary>
    /// Проверяет наличие игроков в радиусе захвата (оптимизированная версия)
    /// </summary>
    private void CheckForNearbyPlayers() {
        PlayerController closestPlayer = null;
        float closestDistance = float.MaxValue;

        // Ищем ближайшего игрока в расширенном радиусе для подсветки
        float highlightRadius = grabRadius * 1.5f; // Увеличиваем радиус для подсветки
        float highlightRadiusSqr = highlightRadius * highlightRadius; // Используем квадрат для оптимизации

        Vector3 holdPos = transform.position;

        // Используем кэшированный список игроков
        var players = GetCachedPlayers();
        foreach (var player in players) {
            if (player == null || !player.health.IsAlive || player.IsClimbing) continue;

            // Быстрая проверка расстояния через квадрат (избегаем Sqrt)
            Vector3 playerPos = player.transform.position;
            float sqrDistance = (holdPos - playerPos).sqrMagnitude;

            if (sqrDistance <= highlightRadiusSqr && sqrDistance < closestDistance) {
                closestDistance = sqrDistance;
                closestPlayer = player;
            }
        }

        nearbyPlayer = closestPlayer;
    }

    /// <summary>
    /// Принудительно обновить кэш игроков (вызывать при подключении/отключении игроков)
    /// </summary>
    public static void RefreshPlayersCache() {
        cachedPlayers = null;
        lastPlayersCacheTime = 0f;
    }



    /// <summary>
    /// Обновляет визуальную обратную связь (подсветка)
    /// </summary>
    private void UpdateVisualFeedback() {
        bool shouldHighlight = nearbyPlayer != null && !IsOccupied && IsCanBeUse();

        if (highlightEffect != null) {
            highlightEffect.SetActive(shouldHighlight);
        }

        // Обновляем состояние для отслеживания изменений
        wasPlayerNearby = nearbyPlayer != null;
    }

    /// <summary>
    /// Может ли зацепка быть использована
    /// </summary>
    public override bool IsCanBeUse() {
        return IsAvailable && !IsOccupied;
    }

    /// <summary>
    /// Запрос на использование зацепки (захват)
    /// </summary>
    public override void RequestToUse(PlayerRef playerRef) {
        if (!IsCanBeUse()) return;

        PlayerController player = Runner.GetPlayerObject(playerRef)?.GetComponent<PlayerController>();
        if (player == null) return;

        // Проверяем, что игрок в радиусе захвата
        float distance = Vector3.Distance(transform.position, player.transform.position);
        if (distance > grabRadius) return;

        // Захватываем зацепку
        RPC_GrabHold(playerRef);
    }

    /// <summary>
    /// Запрос на отпускание зацепки
    /// </summary>
    public override void RequestDrop() {
        if (IsOccupied && CurrentClimber != PlayerRef.None) {
            RPC_ReleaseHold(CurrentClimber);
        }
    }

    /// <summary>
    /// RPC для захвата зацепки
    /// </summary>
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    private void RPC_GrabHold(PlayerRef playerRef) {
        if (!IsCanBeUse()) return;

        PlayerController player = Runner.GetPlayerObject(playerRef)?.GetComponent<PlayerController>();
        if (player == null) return;

        // Устанавливаем состояние зацепки
        IsOccupied = true;
        CurrentClimber = playerRef;
        // НЕ меняем IsAvailable - зацепка остается доступной для использования

        // Уведомляем игрока о захвате
        player.StartClimbing(this);
    }

    /// <summary>
    /// RPC для отпускания зацепки
    /// </summary>
    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    private void RPC_ReleaseHold(PlayerRef playerRef) {
        if (!IsOccupied || CurrentClimber != playerRef) return;

        PlayerController player = Runner.GetPlayerObject(playerRef)?.GetComponent<PlayerController>();
        if (player == null) return;

        // Сбрасываем состояние зацепки
        IsOccupied = false;
        CurrentClimber = PlayerRef.None;

        // Уведомляем игрока об отпускании
        player.StopClimbing();
    }

    /// <summary>
    /// Получить позицию точки захвата
    /// </summary>
    public Vector3 GetHoldPosition() {
        return holdPoint.position;
    }

    /// <summary>
    /// Получить поворот точки захвата
    /// </summary>
    public Quaternion GetHoldRotation() {
        return holdPoint.rotation;
    }

    /// <summary>
    /// Проверить, находится ли игрок в радиусе захвата
    /// </summary>
    public bool IsPlayerInRange(PlayerController player) {
        if (player == null) return false;
        return Vector3.Distance(transform.position, player.transform.position) <= grabRadius;
    }

    // Методы InteractableItemBase (не используются для зацепок)
    public override void AttachToPlayer(PlayerController player) {
        // Зацепки не подбираются
    }

    public override void ItemAbility(PlayerController player) {
        // У зацепок нет способностей
    }

    // Отображение гизмо в редакторе
    private void OnDrawGizmosSelected() {
        if (!showGizmos) return;

        // Радиус захвата
        Gizmos.color = IsOccupied ? Color.red : Color.green;
        Gizmos.DrawWireSphere(transform.position, grabRadius);

        // Точка захвата
        if (holdPoint != null) {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(holdPoint.position, Vector3.one * 0.2f);

            // Линия от центра к точке захвата
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, holdPoint.position);
        }
    }
}
